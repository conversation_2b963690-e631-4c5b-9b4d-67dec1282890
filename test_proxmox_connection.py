#!/usr/bin/env python3
"""
Test script to verify Proxmox connection with the configured settings.
Run this script to ensure the Proxmox server is accessible and credentials are correct.
"""

import os
import sys
from dotenv import load_dotenv

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from proxmoxer import ProxmoxAPI
    from backend.app.services.proxmox import ProxmoxService
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please ensure you have installed the backend dependencies:")
    print("cd backend && pip install -r requirements.txt")
    sys.exit(1)

def test_proxmox_connection():
    """Test the Proxmox connection using the configured settings."""
    
    # Load environment variables
    load_dotenv(os.path.join('backend', '.env'))
    
    # Get configuration from environment
    host = os.getenv("PROXMOX_HOST")
    user = os.getenv("PROXMOX_USER")
    password = os.getenv("PROXMOX_PASSWORD")
    
    print("Proxmox Connection Test")
    print("=" * 50)
    print(f"Host: {host}")
    print(f"User: {user}")
    print(f"Password: {'*' * len(password) if password else 'Not set'}")
    print()
    
    if not all([host, user, password]):
        print("❌ Error: Missing Proxmox configuration in backend/.env")
        print("Please ensure PROXMOX_HOST, PROXMOX_USER, and PROXMOX_PASSWORD are set.")
        return False
    
    try:
        print("🔄 Testing direct ProxmoxAPI connection...")
        
        # Test direct connection
        proxmox = ProxmoxAPI(
            host=host,
            user=user,
            password=password,
            verify_ssl=False
        )
        
        # Test basic API call
        version = proxmox.version.get()
        print(f"✅ Connected successfully!")
        print(f"   Proxmox Version: {version.get('version', 'Unknown')}")
        print(f"   Release: {version.get('release', 'Unknown')}")
        print()
        
        # Test nodes
        print("🔄 Testing node access...")
        nodes = proxmox.nodes.get()
        print(f"✅ Found {len(nodes)} node(s):")
        for node in nodes:
            status_icon = "🟢" if node['status'] == 'online' else "🔴"
            print(f"   {status_icon} {node['node']} - {node['status']}")
        print()
        
        # Test cluster resources
        print("🔄 Testing cluster resources...")
        resources = proxmox.cluster.resources.get()
        vms = [r for r in resources if r['type'] in ['qemu', 'lxc']]
        print(f"✅ Found {len(vms)} VM(s)/Container(s)")
        print()
        
        # Test ProxmoxService
        print("🔄 Testing ProxmoxService class...")
        service = ProxmoxService()
        
        # Test async methods would require asyncio, so we'll just test initialization
        print("✅ ProxmoxService initialized successfully")
        print()
        
        print("🎉 All tests passed! Proxmox connection is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        print()
        print("Troubleshooting tips:")
        print("1. Verify the Proxmox server is running and accessible")
        print("2. Check if the IP address is correct: ping *************")
        print("3. Verify web access: https://*************:8006/")
        print("4. Ensure the credentials are correct")
        print("5. Check if API access is enabled for the root user")
        print("6. Verify firewall settings")
        return False

if __name__ == "__main__":
    success = test_proxmox_connection()
    sys.exit(0 if success else 1)
