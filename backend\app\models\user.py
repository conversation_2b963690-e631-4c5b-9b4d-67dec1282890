from sqlalchemy import <PERSON>umn, Integer, String, <PERSON><PERSON><PERSON>, Enum
from sqlalchemy.orm import relationship
from .base import Base, BaseModel
import enum

class UserRole(enum.Enum):
    STUDENT = "student"
    TEACHER = "teacher"
    ADMIN = "admin"

class User(BaseModel):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    role = Column(Enum(UserRole), nullable=False, default=UserRole.STUDENT)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    virtual_machines = relationship("VirtualMachine", back_populates="owner")
    
    def __repr__(self):
        return f"<User {self.username}>"