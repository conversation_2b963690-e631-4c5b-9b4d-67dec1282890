import React from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  Grid,
  Chip,
  Avatar,
  useTheme,
} from '@mui/material';
import {
  Computer,
  Memory,
  Storage,
  NetworkCheck,
  CheckCircle,
  Warning,
  Error,
} from '@mui/icons-material';

interface SystemOverviewProps {
  systemInfo: {
    cluster: {
      total_nodes: number;
      online_nodes: number;
      health: string;
    };
    virtual_machines: {
      total_vms: number;
      running_vms: number;
      stopped_vms: number;
      suspended_vms: number;
      vm_types: {
        kvm: number;
        lxc: number;
      };
      resource_allocation: {
        total_cpu_cores: number;
        total_memory_mb: number;
        total_disk_gb: number;
      };
    };
  };
}

const SystemOverview: React.FC<SystemOverviewProps> = ({ systemInfo }) => {
  const theme = useTheme();

  const getHealthIcon = (health: string) => {
    switch (health.toLowerCase()) {
      case 'healthy':
        return <CheckCircle sx={{ color: theme.palette.success.main }} />;
      case 'degraded':
        return <Warning sx={{ color: theme.palette.warning.main }} />;
      case 'critical':
        return <Error sx={{ color: theme.palette.error.main }} />;
      default:
        return <CheckCircle sx={{ color: theme.palette.text.secondary }} />;
    }
  };

  const getHealthColor = (health: string) => {
    switch (health.toLowerCase()) {
      case 'healthy':
        return theme.palette.success.main;
      case 'degraded':
        return theme.palette.warning.main;
      case 'critical':
        return theme.palette.error.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const overviewItems = [
    {
      title: 'Cluster Nodes',
      value: `${systemInfo.cluster.online_nodes}/${systemInfo.cluster.total_nodes}`,
      subtitle: 'Online',
      icon: <Computer />,
      color: theme.palette.primary.main,
    },
    {
      title: 'Virtual Machines',
      value: systemInfo.virtual_machines.total_vms,
      subtitle: `${systemInfo.virtual_machines.running_vms} running`,
      icon: <Memory />,
      color: theme.palette.secondary.main,
    },
    {
      title: 'CPU Cores',
      value: systemInfo.virtual_machines.resource_allocation.total_cpu_cores,
      subtitle: 'Allocated',
      icon: <NetworkCheck />,
      color: theme.palette.info.main,
    },
    {
      title: 'Memory',
      value: `${(systemInfo.virtual_machines.resource_allocation.total_memory_mb / 1024).toFixed(1)} GB`,
      subtitle: 'Allocated',
      icon: <Storage />,
      color: theme.palette.warning.main,
    },
  ];

  return (
    <Card
      sx={{
        background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.action.hover} 100%)`,
        border: `1px solid ${theme.palette.divider}`,
      }}
    >
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 3,
          }}
        >
          <Typography variant="h6" fontWeight={600} color="textPrimary">
            System Overview
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getHealthIcon(systemInfo.cluster.health)}
            <Chip
              label={systemInfo.cluster.health.toUpperCase()}
              size="small"
              sx={{
                backgroundColor: getHealthColor(systemInfo.cluster.health),
                color: 'white',
                fontWeight: 600,
                fontSize: '0.75rem',
              }}
            />
          </Box>
        </Box>

        <Grid container spacing={3}>
          {overviewItems.map((item, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  p: 2,
                  borderRadius: 2,
                  backgroundColor: theme.palette.background.paper,
                  border: `1px solid ${theme.palette.divider}`,
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: theme.shadows[4],
                  },
                }}
              >
                <Avatar
                  sx={{
                    backgroundColor: `${item.color}20`,
                    color: item.color,
                    width: 48,
                    height: 48,
                  }}
                >
                  {item.icon}
                </Avatar>
                <Box>
                  <Typography
                    variant="h5"
                    fontWeight={700}
                    color="textPrimary"
                    sx={{ lineHeight: 1.2 }}
                  >
                    {item.value}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="textSecondary"
                    fontWeight={500}
                  >
                    {item.title}
                  </Typography>
                  <Typography
                    variant="caption"
                    color="textSecondary"
                    sx={{ fontSize: '0.75rem' }}
                  >
                    {item.subtitle}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 3, pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                VM Types Distribution
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label={`KVM: ${systemInfo.virtual_machines.vm_types.kvm}`}
                  size="small"
                  variant="outlined"
                  sx={{ fontWeight: 500 }}
                />
                <Chip
                  label={`LXC: ${systemInfo.virtual_machines.vm_types.lxc}`}
                  size="small"
                  variant="outlined"
                  sx={{ fontWeight: 500 }}
                />
              </Box>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                VM Status Distribution
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label={`Running: ${systemInfo.virtual_machines.running_vms}`}
                  size="small"
                  sx={{
                    backgroundColor: theme.palette.success.main,
                    color: 'white',
                    fontWeight: 500,
                  }}
                />
                <Chip
                  label={`Stopped: ${systemInfo.virtual_machines.stopped_vms}`}
                  size="small"
                  sx={{
                    backgroundColor: theme.palette.error.main,
                    color: 'white',
                    fontWeight: 500,
                  }}
                />
                {systemInfo.virtual_machines.suspended_vms > 0 && (
                  <Chip
                    label={`Suspended: ${systemInfo.virtual_machines.suspended_vms}`}
                    size="small"
                    sx={{
                      backgroundColor: theme.palette.warning.main,
                      color: 'white',
                      fontWeight: 500,
                    }}
                  />
                )}
              </Box>
            </Grid>
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};

export default SystemOverview;
