# Proxmox Lab Management System - Deployment Guide

This guide provides comprehensive instructions for deploying the Proxmox Lab Management System using Docker in both development and production environments.

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Git
- Access to Proxmox server (*************)

### Development Deployment

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd proxmox-app
   cp .env.development .env
   ```

2. **Start Development Environment**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

3. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Production Deployment

1. **Setup Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your production values
   ```

2. **Generate Secure Keys**
   ```bash
   # Generate a secure secret key
   openssl rand -hex 32
   
   # Generate secure database passwords
   openssl rand -base64 32
   ```

3. **Deploy**
   ```bash
   docker-compose up -d
   ```

4. **Access the Application**
   - Application: http://localhost
   - Health Check: http://localhost/health

## 📋 Detailed Setup Instructions

### Environment Configuration

#### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `SECRET_KEY` | JWT secret key (32+ chars) | `your-super-secure-secret-key` |
| `DB_PASSWORD` | Database password | `secure_db_password` |
| `PROXMOX_HOST` | Proxmox server IP | `*************` |
| `PROXMOX_USER` | Proxmox username | `root@pam` |
| `PROXMOX_PASSWORD` | Proxmox password | `qwerty@123` |

#### Optional Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `ACCESS_TOKEN_EXPIRE_MINUTES` | `60` | JWT token expiration |
| `LOG_LEVEL` | `INFO` | Logging level |
| `ALLOWED_ORIGINS` | `http://localhost` | CORS origins |

### Database Setup

The system uses MySQL 8.0 with automatic initialization:

1. **Database Schema**: Automatically created via Alembic migrations
2. **Default User**: Created on first startup
3. **Backup**: Use Docker volumes for persistence

### SSL/HTTPS Configuration

For production with SSL:

1. **Obtain SSL Certificates**
   ```bash
   # Using Let's Encrypt (example)
   certbot certonly --standalone -d yourdomain.com
   ```

2. **Update nginx configuration**
   ```bash
   # Uncomment HTTPS server block in nginx/conf.d/default.conf
   # Update SSL certificate paths
   ```

3. **Mount SSL certificates**
   ```yaml
   # In docker-compose.yml
   volumes:
     - /etc/letsencrypt/live/yourdomain.com:/etc/nginx/ssl:ro
   ```

## 🔧 Advanced Configuration

### Scaling the Application

#### Horizontal Scaling

```yaml
# docker-compose.yml
services:
  backend:
    deploy:
      replicas: 3
  
  nginx:
    # Update upstream configuration for load balancing
```

#### Resource Limits

```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

### Monitoring and Logging

#### Log Management

```bash
# View logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f nginx

# Log rotation (add to docker-compose.yml)
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

#### Health Monitoring

All services include health checks:
- Backend: `http://backend:8000/health`
- Frontend: `http://frontend:80/`
- Database: MySQL ping
- Guacamole: `http://guacamole:8080/guacamole/`

### Backup Strategy

#### Database Backup

```bash
# Create backup
docker-compose exec db mysqldump -u root -p proxmox_app > backup.sql

# Restore backup
docker-compose exec -T db mysql -u root -p proxmox_app < backup.sql
```

#### Volume Backup

```bash
# Backup volumes
docker run --rm -v proxmox-app_mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql_data.tar.gz -C /data .

# Restore volumes
docker run --rm -v proxmox-app_mysql_data:/data -v $(pwd):/backup alpine tar xzf /backup/mysql_data.tar.gz -C /data
```

## 🛠️ Maintenance

### Updates

1. **Pull latest images**
   ```bash
   docker-compose pull
   ```

2. **Rebuild and restart**
   ```bash
   docker-compose up -d --build
   ```

3. **Database migrations**
   ```bash
   docker-compose exec backend alembic upgrade head
   ```

### Troubleshooting

#### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database status
   docker-compose exec db mysqladmin ping -u root -p
   
   # Check logs
   docker-compose logs db
   ```

2. **Proxmox Connection Failed**
   ```bash
   # Test connection
   docker-compose exec backend python test_proxmox_connection.py
   ```

3. **Frontend Build Failed**
   ```bash
   # Clear node modules and rebuild
   docker-compose exec frontend rm -rf node_modules
   docker-compose up --build frontend
   ```

#### Performance Optimization

1. **Database Optimization**
   ```sql
   # Connect to database
   docker-compose exec db mysql -u root -p
   
   # Optimize tables
   OPTIMIZE TABLE users, virtual_machines;
   ```

2. **Nginx Optimization**
   - Enable gzip compression ✅
   - Configure caching headers ✅
   - Use HTTP/2 (requires SSL)

## 🔒 Security Considerations

### Production Security Checklist

- [ ] Change all default passwords
- [ ] Use strong SECRET_KEY (32+ characters)
- [ ] Enable HTTPS with valid SSL certificates
- [ ] Configure firewall rules
- [ ] Regular security updates
- [ ] Monitor access logs
- [ ] Backup encryption

### Network Security

```bash
# Restrict database access
# Only allow backend container to access database
# Configure in docker-compose.yml networks section
```

### Access Control

- Default role: Student (limited access)
- Admin role: Full system access
- Instructor role: Classroom management

## 📊 Monitoring

### Application Metrics

- Health endpoints: `/health`, `/api/health`
- Database connections
- Proxmox API response times
- User authentication events

### Log Analysis

```bash
# Monitor authentication attempts
docker-compose logs backend | grep "Login attempt"

# Monitor errors
docker-compose logs backend | grep "ERROR"

# Monitor performance
docker-compose logs nginx | grep "upstream"
```

## 🆘 Support

### Getting Help

1. Check logs: `docker-compose logs [service]`
2. Verify configuration: `docker-compose config`
3. Test connectivity: Run connection tests
4. Review documentation: API docs at `/docs`

### Useful Commands

```bash
# Full system restart
docker-compose down && docker-compose up -d

# Reset database (WARNING: Data loss)
docker-compose down -v && docker-compose up -d

# Update single service
docker-compose up -d --no-deps backend

# Scale services
docker-compose up -d --scale backend=3
```
