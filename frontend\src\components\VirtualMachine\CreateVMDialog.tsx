import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Stepper,
  Step,
  StepLabel,
  Card,
  CardContent,
  Chip,
  Alert,
  <PERSON>lider,
  FormControlLabel,
  Switch,
  Divider,
  useTheme,
  alpha,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import {
  Computer,
  Memory,
  Storage,
  Settings,
  Security,
  CheckCircle,
  Warning,
} from '@mui/icons-material';
import { VMType, VMCreateData } from '../../types';
import { RESOURCE_LIMITS } from '../../constants';
import { useVirtualMachines } from '../../hooks/useVirtualMachines';

interface CreateVMDialogProps {
  open: boolean;
  onClose: () => void;
}

interface CreateVMFormData {
  name: string;
  vm_type: VMType;
  cpu_cores: number;
  memory_mb: number;
  disk_size: number;
  rdp_enabled: boolean;
  ssh_enabled: boolean;
  template?: string;
}

const steps = ['Basic Configuration', 'Resource Allocation', 'Network & Security'];

const vmTemplates = [
  { id: 'ubuntu-20.04', name: 'Ubuntu 20.04 LTS', type: VMType.LXC, icon: '🐧' },
  { id: 'ubuntu-22.04', name: 'Ubuntu 22.04 LTS', type: VMType.KVM, icon: '🐧' },
  { id: 'windows-10', name: 'Windows 10', type: VMType.KVM, icon: '🪟' },
  { id: 'centos-8', name: 'CentOS 8', type: VMType.KVM, icon: '🔴' },
  { id: 'debian-11', name: 'Debian 11', type: VMType.LXC, icon: '🌀' },
  { id: 'custom', name: 'Custom Configuration', type: VMType.KVM, icon: '⚙️' },
];

const CreateVMDialog: React.FC<CreateVMDialogProps> = ({ open, onClose }) => {
  const theme = useTheme();
  const { createVM, isLoading, error } = useVirtualMachines();

  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<CreateVMFormData>({
    name: '',
    vm_type: VMType.KVM,
    cpu_cores: RESOURCE_LIMITS.CPU.DEFAULT,
    memory_mb: RESOURCE_LIMITS.MEMORY.DEFAULT,
    disk_size: RESOURCE_LIMITS.DISK.DEFAULT,
    rdp_enabled: false,
    ssh_enabled: true,
    template: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const handleClose = () => {
    setActiveStep(0);
    setFormData({
      name: '',
      vm_type: VMType.KVM,
      cpu_cores: RESOURCE_LIMITS.CPU.DEFAULT,
      memory_mb: RESOURCE_LIMITS.MEMORY.DEFAULT,
      disk_size: RESOURCE_LIMITS.DISK.DEFAULT,
      rdp_enabled: false,
      ssh_enabled: true,
      template: '',
    });
    setFormErrors({});
    onClose();
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 0: // Basic Configuration
        if (!formData.name.trim()) {
          newErrors.name = 'VM name is required';
        } else if (!/^[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/.test(formData.name)) {
          newErrors.name = 'VM name must be lowercase, alphanumeric, and may include hyphens';
        }
        if (!formData.template) {
          newErrors.template = 'Please select a template';
        }
        break;
      case 1: // Resource Allocation
        if (formData.cpu_cores < RESOURCE_LIMITS.CPU.MIN || formData.cpu_cores > RESOURCE_LIMITS.CPU.MAX) {
          newErrors.cpu_cores = `CPU cores must be between ${RESOURCE_LIMITS.CPU.MIN} and ${RESOURCE_LIMITS.CPU.MAX}`;
        }
        if (formData.memory_mb < RESOURCE_LIMITS.MEMORY.MIN || formData.memory_mb > RESOURCE_LIMITS.MEMORY.MAX) {
          newErrors.memory_mb = `Memory must be between ${RESOURCE_LIMITS.MEMORY.MIN} and ${RESOURCE_LIMITS.MEMORY.MAX} MB`;
        }
        if (formData.disk_size < RESOURCE_LIMITS.DISK.MIN || formData.disk_size > RESOURCE_LIMITS.DISK.MAX) {
          newErrors.disk_size = `Disk size must be between ${RESOURCE_LIMITS.DISK.MIN} and ${RESOURCE_LIMITS.DISK.MAX} GB`;
        }
        break;
    }

    setFormErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const handleCreateVM = async () => {
    if (!validateStep(activeStep)) return;

    try {
      const vmData: VMCreateData = {
        name: formData.name,
        vm_type: formData.vm_type,
        cpu_cores: formData.cpu_cores,
        memory_mb: formData.memory_mb,
        disk_size: formData.disk_size,
        rdp_enabled: formData.rdp_enabled,
        ssh_enabled: formData.ssh_enabled,
      };
      await createVM(vmData);
      handleClose();
    } catch (err) {
      console.error('Failed to create VM:', err);
    }
  };



  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
              Choose a Template
            </Typography>
            <Grid container spacing={2}>
              {vmTemplates.map((template) => (
                <Grid item xs={12} sm={6} md={4} key={template.id}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      border: formData.template === template.id ? 2 : 1,
                      borderColor: formData.template === template.id
                        ? theme.palette.primary.main
                        : theme.palette.divider,
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: theme.shadows[8],
                      },
                    }}
                    onClick={() => {
                      setFormData(prev => ({
                        ...prev,
                        template: template.id,
                        vm_type: template.type,
                      }));
                      setFormErrors(prev => ({ ...prev, template: '' }));
                    }}
                  >
                    <CardContent sx={{ textAlign: 'center', py: 3 }}>
                      <Typography variant="h3" sx={{ mb: 1 }}>
                        {template.icon}
                      </Typography>
                      <Typography variant="h6" gutterBottom>
                        {template.name}
                      </Typography>
                      <Chip
                        label={template.type}
                        size="small"
                        color={template.type === VMType.KVM ? 'primary' : 'secondary'}
                        sx={{ mt: 1 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
            {formErrors.template && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {formErrors.template}
              </Alert>
            )}

            <Divider sx={{ my: 3 }} />

            <TextField
              fullWidth
              label="VM Name"
              value={formData.name}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, name: e.target.value }));
                setFormErrors(prev => ({ ...prev, name: '' }));
              }}
              error={!!formErrors.name}
              helperText={formErrors.name || 'Enter a unique name for your virtual machine'}
              sx={{ mt: 2 }}
            />
          </Box>
        );

      case 1:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
              Resource Allocation
            </Typography>

            <Grid container spacing={4}>
              <Grid item xs={12}>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Computer sx={{ mr: 1, color: theme.palette.primary.main }} />
                    <Typography variant="subtitle1" fontWeight={600}>
                      CPU Cores: {formData.cpu_cores}
                    </Typography>
                  </Box>
                  <Slider
                    value={formData.cpu_cores}
                    onChange={(_, value) => setFormData(prev => ({ ...prev, cpu_cores: value as number }))}
                    min={RESOURCE_LIMITS.CPU.MIN}
                    max={RESOURCE_LIMITS.CPU.MAX}
                    step={1}
                    marks
                    valueLabelDisplay="auto"
                    sx={{ ml: 2 }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Memory sx={{ mr: 1, color: theme.palette.secondary.main }} />
                    <Typography variant="subtitle1" fontWeight={600}>
                      Memory: {(formData.memory_mb / 1024).toFixed(1)} GB
                    </Typography>
                  </Box>
                  <Slider
                    value={formData.memory_mb}
                    onChange={(_, value) => setFormData(prev => ({ ...prev, memory_mb: value as number }))}
                    min={RESOURCE_LIMITS.MEMORY.MIN}
                    max={RESOURCE_LIMITS.MEMORY.MAX}
                    step={512}
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${(value / 1024).toFixed(1)} GB`}
                    sx={{ ml: 2 }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Storage sx={{ mr: 1, color: theme.palette.warning.main }} />
                    <Typography variant="subtitle1" fontWeight={600}>
                      Disk Size: {formData.disk_size} GB
                    </Typography>
                  </Box>
                  <Slider
                    value={formData.disk_size}
                    onChange={(_, value) => setFormData(prev => ({ ...prev, disk_size: value as number }))}
                    min={RESOURCE_LIMITS.DISK.MIN}
                    max={RESOURCE_LIMITS.DISK.MAX}
                    step={5}
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${value} GB`}
                    sx={{ ml: 2 }}
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
        );

      case 2:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
              Network & Security Settings
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card sx={{ p: 3, backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Security sx={{ mr: 1, color: theme.palette.primary.main }} />
                    <Typography variant="subtitle1" fontWeight={600}>
                      Remote Access Configuration
                    </Typography>
                  </Box>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.ssh_enabled}
                        onChange={(e) => setFormData(prev => ({ ...prev, ssh_enabled: e.target.checked }))}
                        color="primary"
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body1" fontWeight={500}>
                          Enable SSH Access
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Allow secure shell access to the virtual machine
                        </Typography>
                      </Box>
                    }
                    sx={{ mb: 2, alignItems: 'flex-start' }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.rdp_enabled}
                        onChange={(e) => setFormData(prev => ({ ...prev, rdp_enabled: e.target.checked }))}
                        color="primary"
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body1" fontWeight={500}>
                          Enable RDP Access
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Allow remote desktop access (Windows VMs only)
                        </Typography>
                      </Box>
                    }
                    sx={{ alignItems: 'flex-start' }}
                  />
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Card sx={{ p: 3, backgroundColor: alpha(theme.palette.success.main, 0.05) }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <CheckCircle sx={{ mr: 1, color: theme.palette.success.main }} />
                    <Typography variant="subtitle1" fontWeight={600}>
                      Configuration Summary
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="textSecondary">Template:</Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {vmTemplates.find(t => t.id === formData.template)?.name || 'Not selected'}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="textSecondary">VM Name:</Typography>
                      <Typography variant="body1" fontWeight={500}>{formData.name || 'Not set'}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="textSecondary">CPU Cores:</Typography>
                      <Typography variant="body1" fontWeight={500}>{formData.cpu_cores}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="textSecondary">Memory:</Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {(formData.memory_mb / 1024).toFixed(1)} GB
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="textSecondary">Disk Size:</Typography>
                      <Typography variant="body1" fontWeight={500}>{formData.disk_size} GB</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="textSecondary">Access:</Typography>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        {formData.ssh_enabled && <Chip label="SSH" size="small" color="primary" />}
                        {formData.rdp_enabled && <Chip label="RDP" size="small" color="secondary" />}
                        {!formData.ssh_enabled && !formData.rdp_enabled && (
                          <Typography variant="body2" color="textSecondary">None</Typography>
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                </Card>
              </Grid>
            </Grid>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
        }
      }}
    >
      <DialogTitle
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          color: 'white',
          textAlign: 'center',
          py: 3,
        }}
      >
        <Typography variant="h4" fontWeight={700}>
          Create Virtual Machine
        </Typography>
        <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 1 }}>
          Set up your new virtual environment in just a few steps
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3 }}>
          <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {typeof error === 'string' ? error : error.message || 'An error occurred'}
            </Alert>
          )}

          {renderStepContent(activeStep)}
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          p: 3,
          borderTop: `1px solid ${theme.palette.divider}`,
          background: alpha(theme.palette.background.default, 0.5),
        }}
      >
        <Button onClick={handleClose} disabled={isLoading}>
          Cancel
        </Button>

        {activeStep > 0 && (
          <Button onClick={handleBack} disabled={isLoading}>
            Back
          </Button>
        )}

        {activeStep < steps.length - 1 ? (
          <Button
            variant="contained"
            onClick={handleNext}
            disabled={isLoading}
            sx={{ minWidth: 100 }}
          >
            Next
          </Button>
        ) : (
          <LoadingButton
            variant="contained"
            onClick={handleCreateVM}
            loading={isLoading}
            startIcon={<CheckCircle />}
            sx={{ minWidth: 120 }}
          >
            Create VM
          </LoadingButton>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CreateVMDialog;
