@echo off
setlocal enabledelayedexpansion

REM Proxmox Lab Management System - Setup Script (Windows)
REM This script helps you set up the application for development or production

echo ==================================================
echo   Proxmox Lab Management System Setup
echo ==================================================
echo.

REM Parse arguments
set ENV_TYPE=production
set SKIP_DEPLOY=false

:parse_args
if "%1"=="-d" set ENV_TYPE=development
if "%1"=="--development" set ENV_TYPE=development
if "%1"=="-p" set ENV_TYPE=production
if "%1"=="--production" set ENV_TYPE=production
if "%1"=="--skip-deploy" set SKIP_DEPLOY=true
if "%1"=="-h" goto show_help
if "%1"=="--help" goto show_help
shift
if not "%1"=="" goto parse_args

echo Setting up for %ENV_TYPE% environment
echo.

REM Check prerequisites
echo Checking prerequisites...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not installed. Please install Docker first.
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker Compose is not installed. Please install Docker Compose first.
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

REM Setup environment file
echo Setting up environment file for %ENV_TYPE%...

if exist .env (
    echo WARNING: Environment file already exists. Creating backup...
    copy .env .env.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2% >nul
)

if "%ENV_TYPE%"=="development" (
    copy .env.development .env >nul
) else (
    REM Create production environment file
    (
        echo # Production Environment Configuration
        echo # Generated on %date% %time%
        echo.
        echo # Database Configuration
        echo DB_ROOT_PASSWORD=change_this_secure_password
        echo DB_PASSWORD=change_this_secure_password
        echo.
        echo # Backend Configuration
        echo SECRET_KEY=change-this-to-a-secure-32-character-secret-key
        echo ACCESS_TOKEN_EXPIRE_MINUTES=60
        echo.
        echo # Proxmox Configuration
        echo PROXMOX_HOST=*************
        echo PROXMOX_USER=root@pam
        echo PROXMOX_PASSWORD=qwerty@123
        echo.
        echo # Guacamole Configuration
        echo GUACAMOLE_USERNAME=guacadmin
        echo GUACAMOLE_PASSWORD=guacadmin
        echo GUACAMOLE_DB_ROOT_PASSWORD=change_this_secure_password
        echo GUACAMOLE_DB_PASSWORD=change_this_secure_password
        echo.
        echo # Frontend Configuration
        echo REACT_APP_API_URL=http://localhost/api
        echo REACT_APP_GUACAMOLE_URL=http://localhost/guacamole
        echo REACT_APP_ENABLE_CONSOLE_ACCESS=true
        echo REACT_APP_ENABLE_FILE_TRANSFER=true
        echo REACT_APP_DEBUG=false
        echo.
        echo # Security Configuration
        echo ALLOWED_ORIGINS=http://localhost,https://localhost
        echo.
        echo # Logging Configuration
        echo LOG_LEVEL=INFO
    ) > .env
)

echo ✅ Environment file created: .env
echo.

if "%SKIP_DEPLOY%"=="true" (
    echo ✅ Environment setup completed. Run deployment manually:
    if "%ENV_TYPE%"=="development" (
        echo   docker-compose -f docker-compose.dev.yml up -d
    ) else (
        echo   docker-compose up -d
    )
    goto end
)

REM Deploy application
echo Deploying application in %ENV_TYPE% mode...

if "%ENV_TYPE%"=="development" (
    docker-compose -f docker-compose.dev.yml up -d
) else (
    docker-compose up -d
)

if errorlevel 1 (
    echo ERROR: Deployment failed. Check the logs for details.
    exit /b 1
)

echo ✅ Application deployed successfully
echo.

REM Wait for services
echo Waiting for services to be ready...
timeout /t 10 /nobreak >nul

echo ✅ Services should be ready
echo.

REM Show access information
echo ==================================================
echo ✅ Deployment completed successfully!
echo ==================================================
echo.
echo Access Information:

if "%ENV_TYPE%"=="development" (
    echo   Frontend:     http://localhost:3000
    echo   Backend API:  http://localhost:8000
    echo   API Docs:     http://localhost:8000/docs
    echo   Database:     localhost:3306
) else (
    echo   Application:  http://localhost
    echo   Health Check: http://localhost/health
)

echo.
echo Default Admin Account:
echo   Username: admin
echo   Password: admin ^(change after first login^)
echo.
echo Useful Commands:
echo   View logs:    docker-compose logs -f
echo   Stop:         docker-compose down
echo   Restart:      docker-compose restart
echo.
echo ⚠️  Remember to:
echo   1. Change default passwords in .env file
echo   2. Configure SSL for production
echo   3. Set up regular backups
echo   4. Monitor application logs

goto end

:show_help
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   -d, --development    Setup for development
echo   -p, --production     Setup for production ^(default^)
echo   --skip-deploy        Only setup environment, don't deploy
echo   -h, --help          Show this help message
goto end

:end
echo.
pause
