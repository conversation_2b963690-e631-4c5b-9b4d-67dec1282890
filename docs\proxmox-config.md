# Proxmox Configuration

This document contains the Proxmox server configuration details for the Lab Management System.

## Proxmox Server Details

### Web Access
- **URL**: https://*************:8006/
- **Username**: root
- **Password**: qwerty@123

### Network Configuration
- **IP Address**: *************/26
- **Subnet Mask**: ***************
- **Gateway**: *************
- **Network Range**: ************/26 (************ - *************)

## Application Configuration

### Backend Environment Variables
The following environment variables are configured in `backend/.env`:

```ini
# Proxmox Configuration
PROXMOX_HOST=*************
PROXMOX_USER=root@pam
PROXMOX_PASSWORD=qwerty@123
```

### Network Considerations
- The Proxmox server is accessible on the local network segment ************/26
- Ensure the application server can reach ************* on port 8006 (HTTPS)
- VMs created through this system will be on the same network segment by default

### Security Notes
- The Proxmox credentials are stored in environment variables
- SSL verification is disabled in the ProxmoxService for development
- Consider enabling SSL verification and using proper certificates in production

## VM Network Configuration

### Default VM Network Settings
- **Bridge**: vmbr0 (default Proxmox bridge)
- **Network**: Same subnet as Proxmox host (************/26)
- **DHCP**: Enabled by default for LXC containers
- **Gateway**: *************

### Available IP Range for VMs
- **Start**: ************
- **End**: *************
- **Reserved**: ************* (Proxmox host)
- **Gateway**: *************

## Troubleshooting

### Connection Issues
1. Verify Proxmox server is accessible: `ping *************`
2. Check HTTPS access: `curl -k https://*************:8006/`
3. Verify credentials through Proxmox web interface
4. Check firewall settings on both application server and Proxmox host

### API Access
- Ensure the root user has API access enabled in Proxmox
- Check that the Proxmox API is accessible from the application server
- Verify SSL/TLS settings if connection issues occur

## References
- [Proxmox VE API Documentation](https://pve.proxmox.com/pve-docs/api-viewer/)
- [Proxmox VE Network Configuration](https://pve.proxmox.com/wiki/Network_Configuration)
