# Development Environment Configuration

# Database Configuration
DB_ROOT_PASSWORD=dev_root_password
DB_PASSWORD=dev_password

# Backend Configuration
SECRET_KEY=development-secret-key-not-for-production-use-only
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Proxmox Configuration
PROXMOX_HOST=*************
PROXMOX_USER=root@pam
PROXMOX_PASSWORD=qwerty@123

# Guacamole Configuration
GUACAMOLE_USERNAME=guacadmin
GUACAMOLE_PASSWORD=guacadmin
GUACAMOLE_DB_ROOT_PASSWORD=guacamole_dev_root
GUACAMOLE_DB_PASSWORD=guacamole_dev_password

# Frontend Configuration
REACT_APP_API_URL=http://localhost/api
REACT_APP_GUACAMOLE_URL=http://localhost/guacamole
REACT_APP_ENABLE_CONSOLE_ACCESS=true
REACT_APP_ENABLE_FILE_TRANSFER=true
REACT_APP_DEBUG=true

# Security Configuration
ALLOWED_ORIGINS=http://localhost,http://localhost:3000,http://localhost:8000

# Logging Configuration
LOG_LEVEL=DEBUG
