version: '3.8'

services:
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
      target: builder
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://proxmox_user:${DB_PASSWORD:-dev_password}@db:3306/proxmox_app
      - SECRET_KEY=${SECRET_KEY:-development-secret-key-not-for-production}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      - PROXMOX_HOST=${PROXMOX_HOST:-*************}
      - PROXMOX_USER=${PROXMOX_USER:-root@pam}
      - PROXMOX_PASSWORD=${PROXMOX_PASSWORD:-qwerty@123}
      - DEBUG=True
      - LOG_LEVEL=DEBUG
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    depends_on:
      - db
    networks:
      - lab-net
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: build
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_DEBUG=true
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - lab-net
    command: ["npm", "start"]

  db:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD:-dev_root_password}
      - MYSQL_DATABASE=proxmox_app
      - MYSQL_USER=proxmox_user
      - MYSQL_PASSWORD=${DB_PASSWORD:-dev_password}
    volumes:
      - mysql_data_dev:/var/lib/mysql
    networks:
      - lab-net
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql_data_dev:
    driver: local
  backend_logs:
    driver: local

networks:
  lab-net:
    driver: bridge
