import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  useTheme,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  TrendingFlat,
} from '@mui/icons-material';

interface SystemMetricsCardProps {
  title: string;
  value: number;
  unit?: string;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  trend?: 'up' | 'down' | 'flat';
  trendValue?: number;
  status?: 'normal' | 'warning' | 'critical';
}

const SystemMetricsCard: React.FC<SystemMetricsCardProps> = ({
  title,
  value,
  unit = '%',
  icon,
  color,
  trend,
  trendValue,
  status = 'normal',
}) => {
  const theme = useTheme();

  const getStatusColor = () => {
    switch (status) {
      case 'warning':
        return theme.palette.warning.main;
      case 'critical':
        return theme.palette.error.main;
      default:
        return theme.palette.success.main;
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp fontSize="small" />;
      case 'down':
        return <TrendingDown fontSize="small" />;
      case 'flat':
        return <TrendingFlat fontSize="small" />;
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return theme.palette.error.main;
      case 'down':
        return theme.palette.success.main;
      case 'flat':
        return theme.palette.text.secondary;
      default:
        return theme.palette.text.secondary;
    }
  };

  return (
    <Card
      sx={{
        height: '100%',
        background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.action.hover} 100%)`,
        border: `1px solid ${theme.palette.divider}`,
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[8],
        },
      }}
    >
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 2,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box
              sx={{
                color: theme.palette[color].main,
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {icon}
            </Box>
            <Typography variant="h6" color="textSecondary" fontWeight={500}>
              {title}
            </Typography>
          </Box>
          <Chip
            label={status.toUpperCase()}
            size="small"
            sx={{
              backgroundColor: getStatusColor(),
              color: 'white',
              fontWeight: 600,
              fontSize: '0.75rem',
            }}
          />
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h3"
            fontWeight={700}
            color="textPrimary"
            sx={{ mb: 1 }}
          >
            {value.toFixed(1)}
            <Typography
              component="span"
              variant="h5"
              color="textSecondary"
              sx={{ ml: 0.5 }}
            >
              {unit}
            </Typography>
          </Typography>

          {trend && trendValue !== undefined && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                color: getTrendColor(),
              }}
            >
              {getTrendIcon()}
              <Typography variant="body2" fontWeight={500}>
                {trendValue > 0 ? '+' : ''}{trendValue.toFixed(1)}% from last hour
              </Typography>
            </Box>
          )}
        </Box>

        <Box sx={{ mb: 1 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 0.5,
            }}
          >
            <Typography variant="body2" color="textSecondary">
              Usage
            </Typography>
            <Typography variant="body2" color="textSecondary" fontWeight={500}>
              {value.toFixed(1)}{unit}
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={value}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: theme.palette.action.hover,
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                backgroundColor: getStatusColor(),
              },
            }}
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default SystemMetricsCard;
