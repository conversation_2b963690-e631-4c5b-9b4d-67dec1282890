# Production Environment Configuration
# Copy this file to .env and update with your production values

# Database Configuration
DATABASE_URL=mysql://username:password@db:3306/proxmox_app

# JWT Configuration - IMPORTANT: Generate a secure secret key for production
SECRET_KEY=your-super-secure-secret-key-here-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=60

# Proxmox Configuration
PROXMOX_HOST=*************
PROXMOX_USER=root@pam
PROXMOX_PASSWORD=qwerty@123

# Guacamole Configuration
GUACAMOLE_URL=http://guacamole:8080/guacamole
GUACAMOLE_USERNAME=guacadmin
GUACAMOLE_PASSWORD=guacadmin

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=False
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Security Settings
SECURE_COOKIES=True
HTTPS_ONLY=True

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log

# Performance Settings
WORKERS=4
MAX_CONNECTIONS=100
