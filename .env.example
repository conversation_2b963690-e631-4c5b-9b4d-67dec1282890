# Environment Configuration Example
# Copy this file to .env and update with your values

# Database Configuration
DB_ROOT_PASSWORD=root_secure_password_change_this
DB_PASSWORD=proxmox_secure_password_change_this

# Backend Configuration
SECRET_KEY=your-super-secure-secret-key-minimum-32-characters-change-this
ACCESS_TOKEN_EXPIRE_MINUTES=60

# Proxmox Configuration
PROXMOX_HOST=*************
PROXMOX_USER=root@pam
PROXMOX_PASSWORD=qwerty@123

# Guacamole Configuration
GUACAMOLE_USERNAME=guacadmin
GUACAMOLE_PASSWORD=guacadmin
GUACAMOLE_DB_ROOT_PASSWORD=guacamole_root_password_change_this
GUACAMOLE_DB_PASSWORD=guacamole_secure_password_change_this

# Frontend Configuration
REACT_APP_API_URL=http://localhost/api
REACT_APP_GUACAMOLE_URL=http://localhost/guacamole
REACT_APP_ENABLE_CONSOLE_ACCESS=true
REACT_APP_ENABLE_FILE_TRANSFER=true
REACT_APP_DEBUG=false

# Security Configuration
ALLOWED_ORIGINS=http://localhost,https://localhost,http://yourdomain.com,https://yourdomain.com

# Logging Configuration
LOG_LEVEL=INFO

# SSL Configuration (for production)
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem
