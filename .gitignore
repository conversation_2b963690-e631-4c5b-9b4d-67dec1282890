# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.env
.venv
env/
venv/
ENV/
*.egg-info/
dist/
build/
*.egg

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.env.local
.env.development.local
.env.test.local
.env.production.local
.pnp/
.pnp.js
coverage/
.next/
out/
build/
.env
.DS_Store
*.pem

# IDE
.idea/
.vscode/
*.swp
*.swo
*.swn
*.bak

# Project specific
/closesql.bat
/sql.bat
/project-plan.md
.env
*.log
.coverage
htmlcov/

# Build and Deploy
/docker-compose.yml
backend/Dockerfile
frontend/build/
frontend/dist/

# Database
*.sqlite3
*.db

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db
docs/README.md
docs/setup-checklist.md
docs/troubleshooting.md
docs/workflows.md
