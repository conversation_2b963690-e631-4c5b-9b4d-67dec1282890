#!/bin/bash

# Proxmox Lab Management System - Setup Script
# This script helps you set up the application for development or production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  Proxmox Lab Management System Setup"
    echo "=================================================="
    echo -e "${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Generate secure passwords
generate_passwords() {
    print_info "Generating secure passwords..."
    
    SECRET_KEY=$(openssl rand -hex 32)
    DB_ROOT_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    GUACAMOLE_DB_ROOT_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    GUACAMOLE_DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    
    print_success "Secure passwords generated"
}

# Setup environment file
setup_environment() {
    local env_type=$1
    local env_file=".env"
    
    print_info "Setting up environment file for $env_type..."
    
    if [ -f "$env_file" ]; then
        print_warning "Environment file already exists. Creating backup..."
        cp "$env_file" "$env_file.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    if [ "$env_type" = "development" ]; then
        cp ".env.development" "$env_file"
    else
        # Production setup
        cat > "$env_file" << EOF
# Production Environment Configuration
# Generated on $(date)

# Database Configuration
DB_ROOT_PASSWORD=$DB_ROOT_PASSWORD
DB_PASSWORD=$DB_PASSWORD

# Backend Configuration
SECRET_KEY=$SECRET_KEY
ACCESS_TOKEN_EXPIRE_MINUTES=60

# Proxmox Configuration
PROXMOX_HOST=*************
PROXMOX_USER=root@pam
PROXMOX_PASSWORD=qwerty@123

# Guacamole Configuration
GUACAMOLE_USERNAME=guacadmin
GUACAMOLE_PASSWORD=guacadmin
GUACAMOLE_DB_ROOT_PASSWORD=$GUACAMOLE_DB_ROOT_PASSWORD
GUACAMOLE_DB_PASSWORD=$GUACAMOLE_DB_PASSWORD

# Frontend Configuration
REACT_APP_API_URL=http://localhost/api
REACT_APP_GUACAMOLE_URL=http://localhost/guacamole
REACT_APP_ENABLE_CONSOLE_ACCESS=true
REACT_APP_ENABLE_FILE_TRANSFER=true
REACT_APP_DEBUG=false

# Security Configuration
ALLOWED_ORIGINS=http://localhost,https://localhost

# Logging Configuration
LOG_LEVEL=INFO
EOF
    fi
    
    print_success "Environment file created: $env_file"
}

# Deploy application
deploy_application() {
    local env_type=$1
    
    print_info "Deploying application in $env_type mode..."
    
    if [ "$env_type" = "development" ]; then
        docker-compose -f docker-compose.dev.yml up -d
    else
        docker-compose up -d
    fi
    
    print_success "Application deployed successfully"
}

# Wait for services
wait_for_services() {
    print_info "Waiting for services to be ready..."
    
    # Wait for database
    echo -n "Waiting for database"
    while ! docker-compose exec -T db mysqladmin ping -h localhost --silent; do
        echo -n "."
        sleep 2
    done
    echo ""
    print_success "Database is ready"
    
    # Wait for backend
    echo -n "Waiting for backend"
    while ! curl -f http://localhost:8000/health &>/dev/null; do
        echo -n "."
        sleep 2
    done
    echo ""
    print_success "Backend is ready"
    
    print_success "All services are ready"
}

# Show access information
show_access_info() {
    local env_type=$1
    
    echo ""
    print_header
    print_success "Deployment completed successfully!"
    echo ""
    print_info "Access Information:"
    
    if [ "$env_type" = "development" ]; then
        echo "  Frontend:     http://localhost:3000"
        echo "  Backend API:  http://localhost:8000"
        echo "  API Docs:     http://localhost:8000/docs"
        echo "  Database:     localhost:3306"
    else
        echo "  Application:  http://localhost"
        echo "  Health Check: http://localhost/health"
    fi
    
    echo ""
    print_info "Default Admin Account:"
    echo "  Username: admin"
    echo "  Password: admin (change after first login)"
    echo ""
    print_info "Useful Commands:"
    echo "  View logs:    docker-compose logs -f"
    echo "  Stop:         docker-compose down"
    echo "  Restart:      docker-compose restart"
    echo ""
    print_warning "Remember to:"
    echo "  1. Change default passwords"
    echo "  2. Configure SSL for production"
    echo "  3. Set up regular backups"
    echo "  4. Monitor application logs"
}

# Main script
main() {
    print_header
    
    # Parse arguments
    ENV_TYPE="production"
    SKIP_DEPLOY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--development)
                ENV_TYPE="development"
                shift
                ;;
            -p|--production)
                ENV_TYPE="production"
                shift
                ;;
            --skip-deploy)
                SKIP_DEPLOY=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  -d, --development    Setup for development"
                echo "  -p, --production     Setup for production (default)"
                echo "  --skip-deploy        Only setup environment, don't deploy"
                echo "  -h, --help          Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    print_info "Setting up for $ENV_TYPE environment"
    
    # Run setup steps
    check_prerequisites
    
    if [ "$ENV_TYPE" = "production" ]; then
        generate_passwords
    fi
    
    setup_environment "$ENV_TYPE"
    
    if [ "$SKIP_DEPLOY" = false ]; then
        deploy_application "$ENV_TYPE"
        wait_for_services
        show_access_info "$ENV_TYPE"
    else
        print_success "Environment setup completed. Run deployment manually:"
        if [ "$ENV_TYPE" = "development" ]; then
            echo "  docker-compose -f docker-compose.dev.yml up -d"
        else
            echo "  docker-compose up -d"
        fi
    fi
}

# Run main function
main "$@"
